# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# Context7 MCP Configuration (optional)
CONTEXT7_API_URL=https://context7.example.com/api
CONTEXT7_API_KEY=your_context7_api_key_here

# Ollama Configuration (for local LLM)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:8b

# ChromaDB Configuration
CHROMA_DB_PATH=./chroma_db
CHROMA_COLLECTION_NAME=banana_forge_code

# Logging Configuration
LOG_LEVEL=INFO
VERBOSE=false

# Model Configuration
PRIMARY_MODEL=moonshotai/kimi-k2
LOCAL_MODEL=qwen2.5:8b
MAX_CONCURRENT_AGENTS=5
MAX_ITERATIONS=2

# Performance Configuration
MAX_CODE_SNIPPETS=5
MAX_DOC_SNIPPETS=3
CONTEXT_WINDOW_SIZE=128000
