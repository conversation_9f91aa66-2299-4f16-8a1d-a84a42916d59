# Banana Forge

**AI-powered CLI tool for generating structured feature implementation plans**

Banana Forge is a sophisticated CLI tool designed to help solo developers generate comprehensive, structured feature implementation plans using AI agents. Given a feature description, the tool produces a detailed Markdown report following a 10-agent template that covers all aspects of implementation from core logic to error handling, testing, and deployment.

## Features

- 🤖 **AI-Powered Planning**: Uses advanced language models to generate detailed implementation plans
- 📋 **Structured Templates**: Follows a comprehensive 10-agent template for thorough coverage
- 🔄 **Multi-Model Support**: Integrates both local (Ollama) and remote (OpenRouter) AI models
- 🚀 **Easy to Use**: Simple CLI interface with intuitive commands
- 🧪 **Dry Run Mode**: Test the tool without making API calls
- 📁 **File I/O**: Support for input context files and output to files
- ⚙️ **Configurable**: Flexible configuration via environment variables

## Quick Start

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd banana-forge
   ```

2. **Install dependencies using uv:**
   ```bash
   uv sync
   ```

3. **Set up configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys (see Configuration section below)
   ```

4. **Verify installation:**
   ```bash
   uv run banana-forge --help
   ```

### Basic Usage

1. **Test with dry run mode (no API calls):**
   ```bash
   uv run banana-forge generate "OAuth login system" --dry-run
   ```

2. **Generate a real plan (requires API key):**
   ```bash
   uv run banana-forge generate "Real-time chat feature"
   ```

3. **Save output to file:**
   ```bash
   uv run banana-forge generate "Payment integration" -o payment_plan.md
   ```

4. **Use additional context:**
   ```bash
   uv run banana-forge generate "User dashboard" -i requirements.txt --verbose
   ```

## Configuration

Banana Forge uses environment variables for configuration. Copy `.env.example` to `.env` and configure:

### Required Settings
- `OPENROUTER_API_KEY`: Your OpenRouter API key for remote models

### Optional Settings
- `OLLAMA_BASE_URL`: URL for local Ollama server (default: http://localhost:11434)
- `PRIMARY_MODEL`: Remote model for final synthesis (default: moonshot/kimi-k2)
- `LOCAL_MODEL`: Local model for intermediate tasks (default: qwen2.5:8b)
- `MAX_CONCURRENT_AGENTS`: Number of parallel agents (default: 5)
- `LOG_LEVEL`: Logging level (default: INFO)

## Commands

### Generate Implementation Plan
```bash
banana-forge generate <feature_description> [OPTIONS]
```

**Options:**
- `--input, -i`: Input file with additional context
- `--output, -o`: Output file to save the plan
- `--verbose, -v`: Enable verbose logging
- `--dry-run`: Generate mock response without API calls

### Other Commands
```bash
banana-forge version          # Show version information
banana-forge config           # Show current configuration
```

## Examples

### Basic Feature Planning
```bash
uv run banana-forge generate "User authentication system"
```

### Complex Feature with Context
```bash
uv run banana-forge generate "E-commerce checkout flow" \
  --input project_requirements.md \
  --output checkout_implementation_plan.md \
  --verbose
```

### Testing Without API Calls
```bash
uv run banana-forge generate "Machine learning pipeline" --dry-run
```

### Sample Generated Output

Here's an example of what Banana Forge generates for a feature request:

**Input:**
```bash
uv run banana-forge generate "OAuth login system" --dry-run
```

**Output Preview:**
```markdown
# OAuth login system Implementation Plan

*Generated on 2025-07-17 13:07:59*

## Overview

[DRY RUN] This would provide a comprehensive overview of the OAuth login system feature, including:
- Purpose and business value
- High-level technical approach
- Key components and integrations
- Success criteria

## Current State Analysis

[DRY RUN] This would analyze the current codebase and identify:
- Relevant files, modules, and components
- Existing functionality that relates to this feature
- Potential conflicts or dependencies

## Implementation Phases

[DRY RUN] This would outline implementation phases:
- Phase 1: Foundation setup
- Phase 2: Core functionality
- Phase 3: Integration and testing
- Phase 4: Deployment and monitoring

...
```

*Note: The actual output with API keys configured would include detailed, AI-generated content for each section instead of placeholder text.*

## Development

### Project Structure
```
banana-forge/
├── src/banana_forge/          # Main package
│   ├── cli.py                 # CLI interface
│   ├── core.py                # Core generation logic
│   ├── config.py              # Configuration management
│   ├── llm.py                 # LLM client interface
│   └── templates.py           # Plan templates
├── tests/                     # Test suite
├── docs/                      # Documentation
└── pyproject.toml            # Project configuration
```

### Running Tests
```bash
uv run pytest tests/ -v
```

### Code Quality
```bash
uv run ruff check src/         # Linting
uv run ruff format src/        # Formatting
uv run mypy src/               # Type checking
```

## Architecture

Banana Forge follows a modular architecture designed for extensibility:

1. **CLI Layer**: Typer-based command interface
2. **Core Logic**: Orchestrates the generation process
3. **LLM Integration**: Unified interface for multiple AI providers
4. **Configuration**: Pydantic-based settings management
5. **Templates**: Structured output formatting

### Phase 1 (Current): MVP Implementation
- Single-model generation with basic structure
- CLI interface with essential commands
- Configuration management
- Comprehensive testing

### Phase 2 (Planned): Multi-Agent Orchestration
- Parallel agent execution using LangChain
- Context gathering from code and documentation
- Enhanced output quality and depth

### Phase 3 (Future): Advanced Features
- Integration with development tools
- Performance optimizations
- Extended template library

## Troubleshooting

### Common Issues

**"OPENROUTER_API_KEY is required" Error:**
- Make sure you've copied `.env.example` to `.env`
- Add your OpenRouter API key to the `.env` file
- Get an API key from [OpenRouter](https://openrouter.ai/)

**"Model not found in Ollama" Error:**
- Install Ollama: https://ollama.ai/
- Pull the required model: `ollama pull qwen2.5:8b`
- Ensure Ollama is running: `ollama serve`

**Empty or Poor Quality Output:**
- Check your API key is valid and has credits
- Try using `--verbose` flag to see detailed logs
- For local models, ensure sufficient RAM (8GB+ recommended)

**ChromaDB Issues:**
- Delete the `chroma_db` directory and re-index: `uv run banana-forge index --reset`
- Check file permissions in the project directory

### Getting Help

- Use `--verbose` flag for detailed logging
- Check the `logs/` directory for error details
- Test with `--dry-run` first to verify setup
- Run `uv run banana-forge config` to verify settings

## Testing

### Running Tests

Run the full test suite with coverage:
```bash
uv run pytest tests/ -v
```

### Coverage Reports

The project maintains a minimum test coverage of 69%. Coverage reports are generated automatically:

- **Terminal output**: Shows coverage summary after running tests
- **HTML report**: Detailed coverage report at `htmlcov/index.html`

To view the HTML coverage report:
```bash
# Run tests and open coverage report
python scripts/view-coverage.py
```

### Test Structure

- `tests/` - Unit tests for all modules
- `tests/integration/` - Integration tests (require API keys)
- Coverage threshold: 69% minimum
- Branch coverage enabled for comprehensive testing

### Writing Tests

When adding new functionality:
1. Add unit tests in the appropriate `test_*.py` file
2. Ensure tests cover both success and error cases
3. Mock external dependencies (API calls, file system)
4. Run tests locally before submitting PR

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite: `uv run pytest tests/ -v`
6. Ensure coverage threshold is met
7. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation in the `docs/` directory
- Review the examples in this README

